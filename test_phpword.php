<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Testing PhpWord Library</h1>";

try {
    // Check if vendor directory exists
    if (!is_dir('./vendor')) {
        echo "<p style='color:red'>Error: vendor directory not found</p>";
        exit;
    }
    
    // Check if autoload.php exists
    if (!file_exists('./vendor/autoload.php')) {
        echo "<p style='color:red'>Error: vendor/autoload.php not found</p>";
        exit;
    }
    
    // Try to include the autoloader
    require_once './vendor/autoload.php';
    echo "<p style='color:green'>Successfully loaded autoload.php</p>";
    
    // Check if PhpWord classes exist
    if (!class_exists('PhpOffice\\PhpWord\\PhpWord')) {
        echo "<p style='color:red'>Error: PhpOffice\\PhpWord\\PhpWord class not found</p>";
        exit;
    }
    
    echo "<p style='color:green'>PhpOffice\\PhpWord\\PhpWord class exists</p>";
    
    // Try to create a PhpWord instance
    $phpWord = new \PhpOffice\PhpWord\PhpWord();
    echo "<p style='color:green'>Successfully created PhpWord instance</p>";
    
    // Try to create a section
    $section = $phpWord->addSection();
    echo "<p style='color:green'>Successfully created section</p>";
    
    // Try to add text
    $section->addText('Hello World!');
    echo "<p style='color:green'>Successfully added text</p>";
    
    // Try to create a temp file
    $tempFile = tempnam(sys_get_temp_dir(), 'word');
    if ($tempFile === false) {
        echo "<p style='color:red'>Error: Failed to create temporary file</p>";
        exit;
    }
    echo "<p style='color:green'>Successfully created temporary file: " . htmlspecialchars($tempFile) . "</p>";
    
    // Try to create a writer
    $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
    echo "<p style='color:green'>Successfully created writer</p>";
    
    // Try to save the document
    $objWriter->save($tempFile);
    echo "<p style='color:green'>Successfully saved document to temporary file</p>";
    
    // Check if the file exists and has content
    if (!file_exists($tempFile)) {
        echo "<p style='color:red'>Error: Temporary file does not exist after save</p>";
    } else {
        $fileSize = filesize($tempFile);
        echo "<p style='color:green'>Temporary file exists with size: " . $fileSize . " bytes</p>";
    }
    
    // Clean up
    @unlink($tempFile);
    echo "<p style='color:green'>Successfully deleted temporary file</p>";
    
    echo "<h2>All tests passed successfully!</h2>";
    
} catch (Exception $e) {
    echo "<p style='color:red'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
