{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "5385ee67421ac413e8ff0056e2766af4", "packages": [{"name": "phpoffice/math", "version": "0.2.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/Math.git", "reference": "fc2eb6d1a61b058d5dac77197059db30ee3c8329"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/Math/zipball/fc2eb6d1a61b058d5dac77197059db30ee3c8329", "reference": "fc2eb6d1a61b058d5dac77197059db30ee3c8329", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.88 || ^1.0.0", "phpunit/phpunit": "^7.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\Math\\": "src/Math/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Progi1984", "homepage": "https://lefevre.dev"}], "description": "Math - Manipulate Math Formula", "homepage": "https://phpoffice.github.io/Math/", "keywords": ["MathML", "officemathml", "php"], "support": {"issues": "https://github.com/PHPOffice/Math/issues", "source": "https://github.com/PHPOffice/Math/tree/0.2.0"}, "time": "2024-08-12T07:30:45+00:00"}, {"name": "phpoffice/phpword", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPWord.git", "reference": "8392134ce4b5dba65130ba956231a1602b848b7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPWord/zipball/8392134ce4b5dba65130ba956231a1602b848b7f", "reference": "8392134ce4b5dba65130ba956231a1602b848b7f", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-xml": "*", "php": "^7.1|^8.0", "phpoffice/math": "^0.2"}, "require-dev": {"dompdf/dompdf": "^2.0", "ext-gd": "*", "ext-libxml": "*", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.3", "mpdf/mpdf": "^8.1", "phpmd/phpmd": "^2.13", "phpstan/phpstan-phpunit": "@stable", "phpunit/phpunit": ">=7.0", "symfony/process": "^4.4 || ^5.0", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Allows writing PDF", "ext-gd2": "Allows adding images", "ext-xmlwriter": "Allows writing OOXML and ODF", "ext-xsl": "Allows applying XSL style sheet to headers, to main document part, and to footers of an OOXML template", "ext-zip": "Allows writing OOXML and ODF"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpWord\\": "src/PhpWord"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://gabrielbull.com/"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net/blog/"}, {"name": "<PERSON>", "homepage": "http://ivan.lanin.org"}, {"name": "<PERSON>", "homepage": "http://ru.linkedin.com/pub/roman-syroeshko/34/a53/994/"}, {"name": "<PERSON>"}], "description": "PHPWord - A pure PHP library for reading and writing word processing documents (OOXML, ODF, RTF, HTML, PDF)", "homepage": "https://phpoffice.github.io/PHPWord/", "keywords": ["ISO IEC 29500", "OOXML", "Office Open XML", "OpenDocument", "OpenXML", "PhpOffice", "PhpWord", "Rich Text Format", "WordprocessingML", "doc", "docx", "html", "odf", "odt", "office", "pdf", "php", "reader", "rtf", "template", "template processor", "word", "writer"], "support": {"issues": "https://github.com/PHPOffice/PHPWord/issues", "source": "https://github.com/PHPOffice/PHPWord/tree/1.3.0"}, "time": "2024-08-30T18:03:42+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.6.0"}