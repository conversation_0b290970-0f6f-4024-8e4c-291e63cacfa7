$(document).ready(function() {
    const recordButton = $('#recordButton');
    const outputText = $('#outputText');
    const speakingAnimation = $('#speakingAnimation');
    const thinkingAnimation = $('#thinkingAnimation');
    const languageButtons = $('.language-button');
    const langToggleButton = $('#langToggleButton');
    const languageSwitch = $('#languageSwitch');
    var isRecording = false;
    var isRecognizing = false;
    var isProcessing = false; // To track if we are processing the response
    var isManualStop = false; // To track if the stop action was manual
    var stopTimeout; // Variable to store the timeout ID
    var recognitionLang = '<?=$selLang?>'; // Default language

    function randomWaveBar() {                
        // Add random animation delays to each wave bar
        $('#speakingAnimation .synthesis-bar').each(function() {
            var randomDelay = (Math.random() * 0.6).toFixed(2) + 's';
            $(this).css('animation-delay', randomDelay);
        });
        $('#speakingAnimation .synthesis-bar').addClass('animate');
    }
    randomWaveBar();

    languageButtons.on('click', function() {
        languageButtons.removeClass('active');
        $(this).addClass('active');
        recognitionLang = $(this).data('lang');
        langToggleButton.text($(this).text());
    });

    langToggleButton.on('click', function() {
        languageSwitch.toggle('slide', { direction: 'down' }, 200);
    });

    //languageButtons.first().trigger('click');
    languageSwitch.find('.language-button[data-lang="' + recognitionLang + '"]').trigger('click');

    $(document).on('click', function(event) {
        if (!$(event.target).closest('#languageSwitch, #langToggleButton').length) {
            if (languageSwitch.is(':visible')) {
                languageSwitch.hide('slide', { direction: 'down' }, 200);
            }
        }
    });

    const speechConfig = SpeechSDK.SpeechConfig.fromSubscription("d743ae6ddd9d4e0994233b27c22438b0", "eastasia");
    speechConfig.speechRecognitionLanguage = recognitionLang;
    const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
    const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

    recordButton.on('click', function() {
        $('#streamedSummaryText').empty(); // Clear previous results
        if (isRecording) {
            isManualStop = true;
            // console.log('Stop recording');
            stopRecognition();
        } else {
            // console.log('Start recording');
            isManualStop = false;
            startRecognition();
        }
    });

    function startRecognition() {
        // console.log('Start Recognition');
        if (!isManualStop) {
            if (!isRecognizing && !isProcessing) {
                try {
                    isRecording = true;
                    isRecognizing = true;
                    $('#recordButton').addClass('recording');
                    recognizer.startContinuousRecognitionAsync();
                } catch (error) {
                    console.error('Error starting recognition: ', error);
                }
            } else {
                // console.log('Recognition already started or processing');
            }
        }
    }

    function stopRecognition() {
        // console.log('Stop Recognition');
        if (isRecognizing) {
            isRecording = false;
            isRecognizing = false;
            recordButton.removeClass('recording');
            recognizer.stopContinuousRecognitionAsync();
        } else {
            // console.log('Recognition already stopped');
        }
    }

    recognizer.recognizing = function (s, e) {
        // console.log('Recognizing:', e.result.text);
        outputText.find('.interim').remove();
        outputText.append("<span class='interim'>" + e.result.text + "</span>");
        outputText.get(0).scrollTop = outputText.get(0).scrollHeight;
    };

    recognizer.recognized = function (s, e) {
        if (e.result.text !== undefined && e.result.text !== "") {
            // console.log('Recognized:', e.result.text);
            outputText.find('.interim').remove();
            outputText.append("<span class='final'>" + e.result.text + "</span>");
        }
        if (!isRecognizing) {
            startRecognition();
        }
    };

    recognizer.canceled = function (s, e) {
        stopRecognition();
        // console.error('Canceled:', e);
    };

    recognizer.sessionStarted = function (s, e) {
        // console.log('Session started');
    };

    recognizer.sessionStopped = function (s, e) {
        // console.log('Session stopped');
        isRecognizing = false;
        recordButton.removeClass('recording');
        if (!isProcessing) {
            startRecognition(); // Restart recognition if not processing
        }
    };

    
    //markdown
    marked.setOptions({
        highlight: function (code, language) {
            const validLanguage = hljs.getLanguage(language) ? language : 'javascript';
            return hljs.highlight(code, { language: validLanguage }).value;
        },
    });
    
    function escapeLatex(inputValue) {
        const replacements = [
            { regex: /\\\[/g, replacement: '\\\\[' },
            { regex: /\\\]/g, replacement: '\\\\]' },
            { regex: /\\\(/g, replacement: '\\\\(' },
            { regex: /\\\)/g, replacement: '\\\\)' }
        ];

        return replacements.reduce((acc, { regex, replacement }) => {
            return acc.replace(regex, replacement);
        }, inputValue);
    }

    $('#btnGenerateSummary, #btnReGenerateSummary').on('click', function() {
        $(this).hide();
        const inputText = outputText.text().trim();
        if (inputText.length === 0) {
            alert('No transcript to summarize');
            $(this).show();
            return;
        }

        $('#streamedSummaryText').empty(); // Clear previous results
        thinkingAnimation.show();

        // Make AJAX call to PHP script and handle streaming
        const eventSource = new EventSource(`./streamOpenAIResponse.php?inputText=${encodeURIComponent(inputText)}`);

        eventSource.onmessage = function(event) {
            const data = JSON.parse(event.data);
            let thisContent = data.content;
            $('#streamedSummaryText').append(thisContent);
        };

        eventSource.addEventListener('close', function(event) {
            // console.log('Stream ended.');
            $('#streamedSummaryText').html(marked.parse(escapeLatex($('#streamedSummaryText').text())));
            eventSource.close();
            thinkingAnimation.hide();
            $('#btnReGenerateSummary').show();
        });

        eventSource.onerror = function() {
            eventSource.close();
        };
    });

    $('#tasks-tab').on('click', function() {
        $('#btnGenerateSummary, #btnReGenerateSummary').hide();
        $('#recordButton').show();
    });

    $('#summary-tab').on('click', function() {
        if ($('#streamedSummaryText').html() === '') {
            $('#btnGenerateSummary').show();            
        } else {
            $('#btnReGenerateSummary').show();
        }
        isManualStop = true;
        // console.log('Stop recording');
        stopRecognition();
        $('#recordButton').hide();
    });

});