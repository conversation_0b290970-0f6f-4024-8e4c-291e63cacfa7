<?php
session_start();

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get the content and title from the POST data
$content = isset($_POST['content']) ? $_POST['content'] : '';
$title = isset($_POST['title']) ? $_POST['title'] : 'Document';
$type = isset($_POST['type']) ? $_POST['type'] : 'transcript';

if (empty($content)) {
    http_response_code(400); // Bad Request
    echo json_encode(['error' => 'No content provided']);
    exit;
}

try {
    // Format the content
    $formattedContent = "";
    
    // Add title
    $formattedContent .= strtoupper($title) . "\n";
    $formattedContent .= str_repeat("=", strlen($title)) . "\n\n";
    
    // Add date
    $formattedContent .= "Generated on: " . date('F j, Y, g:i a') . "\n\n";
    
    // Process content based on type
    if ($type === 'summary') {
        // For summary, try to preserve markdown formatting
        $lines = explode("\n", $content);
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Skip empty lines
            if (empty($line)) {
                $formattedContent .= "\n";
                continue;
            }
            
            // Check if it's a heading (starts with # or ##)
            if (preg_match('/^#{1,2}\s+(.+)$/', $line, $matches)) {
                $headingText = $matches[1];
                $formattedContent .= "\n" . strtoupper($headingText) . "\n";
                $formattedContent .= str_repeat("-", strlen($headingText)) . "\n\n";
            } 
            // Check if it's a bullet point
            elseif (preg_match('/^[\*\-]\s+(.+)$/', $line, $matches)) {
                $bulletText = $matches[1];
                $formattedContent .= "* " . $bulletText . "\n";
            }
            // Regular paragraph
            else {
                $formattedContent .= $line . "\n";
            }
        }
    } else {
        // For transcript, just add as paragraphs
        $paragraphs = explode("\n", $content);
        
        foreach ($paragraphs as $paragraph) {
            if (!empty(trim($paragraph))) {
                $formattedContent .= trim($paragraph) . "\n\n";
            }
        }
    }
    
    // Generate a unique filename
    $filename = $type . '_' . date('Ymd_His') . '.txt';
    
    // Set headers for download
    header('Content-Description: File Transfer');
    header('Content-Type: text/plain');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . strlen($formattedContent));
    header('Pragma: public');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    // Output the file
    echo $formattedContent;
    exit;
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error generating text file: ' . $e->getMessage()]);
    exit;
}
?>
