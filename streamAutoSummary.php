<?php
session_start();
require "../lib/Class.AIModel.php";

date_default_timezone_set("Asia/Hong_Kong");

ini_set('output_buffering', 'off');
ini_set('zlib.output_compression', false);

while (@ob_end_flush()) {}
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('X-Accel-Buffering: no');

function chunkMsgToArray($message) {
    $parts = explode("data: ", $message);
    $result = array_filter(array_map(function($item) {
        return trim(str_replace(["\r", "\n", "data:"], '', $item));
    }, array_filter($parts)));
    $message = implode(PHP_EOL, $result);
    $chunks = preg_split('/(?=\{"choices")|(?=\\[DONE\\])/', $message, -1, PREG_SPLIT_NO_EMPTY);
    return $chunks;
}

function generateAutoSummaryFromOpenAI($newTranscript, $existingSummary = "") {
    $aiModel = new AIModel("gpt-4o", "N");
    $apiKey = $aiModel->getApiKey();
    $endpoint = $aiModel->getEndpoint();

    $prompt = "";
    $background = "";
    $background = "background: this is a transcript from a RGC forum. the forum will include different speakers. The forum will mainly use English.";
    if (empty($existingSummary)) {
        // First time generating summary
        $prompt = $background . "
        instructions: 
        - Create a brief summary of the following transcript. 
        - Focus on the main points and key information in chronological order as they appear in the transcript. 
        - Keep it concise and in the same language as the input. 
        - Maintain a clean, organized bullet point format. 
        - don't include any summary heading wording like 'Summary:', or 'Consolidated Summary:', etc., at the beginning. 
        - don't miss any key points
        - some wordings in the transcript may be wrong, you should fix them by the context back and forth, and fix them make sense
        - try to group in speakers
        - in the last Q&A section (Q&A section will be followed by the sharing of all speakers), try to group in Question and Answer, and the answer in the next line, like: Q:<question> <br> A: <answer>
        - the points should display in chronological order base on the transcript
        ";
        
    } else {
        // Updating existing summary
        $prompt =  $background . "
        instructions: 
        - Below is an existing summary followed by new transcript content. 
        - Create a (consolidated and coherent) summary that includes both the existing information and any new key points from the transcript in chronological order. 
        - Avoid repetition and redundancy. 
        - have to group the points by category and speaker. 
        - Use the same language as the input
        - you should keep the content of existing summary unless the new transcript content contradicts it or have to fix the existing summary to make sense, in that case you should fix the existing summary, or you may have to group the new points into previous categories or a new category, and append the new key points to the existing summary by using bullet points
        - but the new transcript content maybe just a small part(lastest part) of the whole conversation, so you should not remove any existing summary that is not contradicted or mentioned by the new transcript content
        - in the last Q&A section (Q&A section will be followed by the sharing of all speakers), try to group the questions and answers together, e.g. Q:<question>\n A:<answer>
        - all the points in the summary output should display in chronological order base on the transcript
        

        EXISTING SUMMARY:
        $existingSummary
        ";
    }

    // last 5000 characters of the transcript
    $newTranscript = substr($newTranscript, -3000);

    $messages = array(
        array("role" => "system", "content" => "You are a helpful assistant that creates concise, non-repetitive summaries of ongoing conversations or lectures. Focus only on the most important information in chronological order and maintain a clean, organized bullet point format."),
        array("role" => "user", "content" => $prompt),
        array("role" => "assistant", "content" => "I'll create a concise and coherent summary of the key points in chronological order following the instructions."),
        array("role" => "user", "content" => "new transcript:" . $newTranscript )
    );

    $data = array(
        //'model' => 'eduhk-DeepSeek-V3-dev-e-us-2',
        'messages' => $messages,
        'temperature' => 0.1,
        "top_p" => $aiModel->getTopP(),
        "frequency_penalty" => $aiModel->getFreqPenalty(),
        "presence_penalty" => $aiModel->getPresencePenalty(),
        "max_tokens" => 8000, // Smaller token limit for faster responses
        'stream' => true // Enable streaming
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "api-key: $apiKey"
    ));
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) {
        $lines = chunkMsgToArray($data);

        $line_c = count($lines);
        foreach($lines as $li=>$line){
            if(trim($line) == '[DONE]') {
                echo "event: close".PHP_EOL;
                echo "data: Connection closed".PHP_EOL.PHP_EOL;
                flush();
                break;
            }
            $line_data = json_decode($line, TRUE);
            if (isset($line_data['choices'][0]['delta']) && isset($line_data['choices'][0]['delta']['content']) && !empty($line_data['choices'][0]['delta']['content'])) {
                echo 'data: '.json_encode(['time'=>date('Y-m-d H:i:s'), 'content'=>$line_data['choices'][0]['delta']['content']]).PHP_EOL.PHP_EOL;
                flush();
            }
        }

        return strlen($data);
    });

    curl_exec($ch);
    curl_close($ch);
}

// Handle POST request to create a session
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['inputText'])) {
    // Generate a unique session ID
    $sessionId = uniqid('auto_summary_', true);

    // Store the input text and existing summary in the session
    $_SESSION[$sessionId] = [
        'newTranscript' => $_POST['inputText'],
        'existingSummary' => isset($_POST['existingSummary']) ? $_POST['existingSummary'] : ""
    ];

    // Return the session ID
    echo $sessionId;
    exit;
}

// Handle GET request with session parameter
if (isset($_GET['session'])) {
    $sessionId = $_GET['session'];

    // Check if the session exists
    if (isset($_SESSION[$sessionId])) {
        $sessionData = $_SESSION[$sessionId];
        $newTranscript = $sessionData['newTranscript'];
        $existingSummary = $sessionData['existingSummary'];

        // Clear the session data after retrieving it
        unset($_SESSION[$sessionId]);

        // Generate the summary
        generateAutoSummaryFromOpenAI($newTranscript, $existingSummary);
    } else {
        echo "event: error\ndata: Invalid or expired session\n\n";
        flush();
    }
} else {
    echo "event: error\ndata: No session ID provided\n\n";
    flush();
}
?>


