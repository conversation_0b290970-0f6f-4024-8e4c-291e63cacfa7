<?php
session_start();
require_once './vendor/autoload.php'; // PhpOffice/PhpWord is installed in the current directory

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Settings;

// Set temporary directory
Settings::setTempDir(sys_get_temp_dir());

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get the content and title from the POST data
$content = isset($_POST['content']) ? $_POST['content'] : '';
$title = isset($_POST['title']) ? $_POST['title'] : 'Document';
$type = isset($_POST['type']) ? $_POST['type'] : 'transcript';

if (empty($content)) {
    http_response_code(400); // Bad Request
    echo json_encode(['error' => 'No content provided']);
    exit;
}

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    // Create a new PhpWord instance
    $phpWord = new PhpWord();

    // Set default font
    $phpWord->setDefaultFontName('Arial');
    $phpWord->setDefaultFontSize(11);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error initializing PhpWord: ' . $e->getMessage()]);
    exit;
}

try {
    // Add a section
    $section = $phpWord->addSection();

    // Add title
    $section->addText(
        $title,
        ['bold' => true, 'size' => 16],
        ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::CENTER, 'spaceAfter' => 240]
    );

    // Add date
    $section->addText(
        'Generated on: ' . date('F j, Y, g:i a'),
        ['italic' => true, 'size' => 10],
        ['alignment' => \PhpOffice\PhpWord\SimpleType\Jc::RIGHT, 'spaceAfter' => 240]
    );

    // Process content based on type
    if ($type === 'summary') {
        // For summary, we'll try to parse markdown
        $lines = explode("\n", $content);

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip empty lines
            if (empty($line)) {
                continue;
            }

            // Check if it's a heading (starts with # or ##)
            if (preg_match('/^#{1,2}\s+(.+)$/', $line, $matches)) {
                $headingText = $matches[1];
                $section->addText(
                    $headingText,
                    ['bold' => true, 'size' => 14],
                    ['spaceAfter' => 120, 'spaceBefore' => 240]
                );
            }
            // Check if it's a bullet point
            elseif (preg_match('/^[\*\-]\s+(.+)$/', $line, $matches)) {
                $bulletText = $matches[1];
                $listItem = $section->addListItem($bulletText, 0);
            }
            // Regular paragraph
            else {
                $section->addText($line, null, ['spaceAfter' => 120]);
            }
        }
    } else {
        // For transcript, just add as paragraphs
        $paragraphs = explode("\n", $content);

        foreach ($paragraphs as $paragraph) {
            if (!empty(trim($paragraph))) {
                $section->addText(trim($paragraph), null, ['spaceAfter' => 120]);
            }
        }
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error processing content: ' . $e->getMessage()]);
    exit;
}

try {
    // Generate a unique filename
    $filename = $type . '_' . date('Ymd_His') . '.docx';
    $tempFile = tempnam(sys_get_temp_dir(), 'word');

    if ($tempFile === false) {
        throw new Exception("Failed to create temporary file");
    }

    // Save the document
    $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
    $objWriter->save($tempFile);

    if (!file_exists($tempFile)) {
        throw new Exception("Failed to save document to temporary file");
    }

    // Set headers for download
    header('Content-Description: File Transfer');
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($tempFile));
    header('Pragma: public');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    // Output the file
    if (!readfile($tempFile)) {
        throw new Exception("Failed to read temporary file");
    }

    // Delete the temporary file
    @unlink($tempFile);
    exit;
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error generating Word document: ' . $e->getMessage()]);
    if (isset($tempFile) && file_exists($tempFile)) {
        @unlink($tempFile);
    }
    exit;
}
?>
