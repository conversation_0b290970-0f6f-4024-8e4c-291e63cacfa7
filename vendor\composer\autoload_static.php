<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit5385ee67421ac413e8ff0056e2766af4
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'PhpOffice\\PhpWord\\' => 18,
            'PhpOffice\\Math\\' => 15,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PhpOffice\\PhpWord\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpoffice/phpword/src/PhpWord',
        ),
        'PhpOffice\\Math\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpoffice/math/src/Math',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit5385ee67421ac413e8ff0056e2766af4::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit5385ee67421ac413e8ff0056e2766af4::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit5385ee67421ac413e8ff0056e2766af4::$classMap;

        }, null, ClassLoader::class);
    }
}
