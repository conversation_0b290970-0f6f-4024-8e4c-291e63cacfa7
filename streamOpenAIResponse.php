<?php
session_start();
require "../lib/Class.AIModel.php";

date_default_timezone_set("Asia/Hong_Kong");

ini_set('output_buffering', 'off');
ini_set('zlib.output_compression', false);

while (@ob_end_flush()) {}
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('X-Accel-Buffering: no');

function chunkMsgToArray($message) {
    $parts = explode("data: ", $message);
    $result = array_filter(array_map(function($item) {
        return trim(str_replace(["\r", "\n", "data:"], '', $item));
    }, array_filter($parts)));
    $message = implode(PHP_EOL, $result);
    $chunks = preg_split('/(?=\{"choices")|(?=\\[DONE\\])/', $message, -1, PREG_SPLIT_NO_EMPTY);
    return $chunks;
}

function generateStreamedResponseFromOpenAI($userInput) {
    $aiModel = new AIModel("gpt-4o", "N");
    $apiKey = $aiModel->getApiKey();
    $endpoint = $aiModel->getEndpoint();

    $messages = array(
        array("role" => "system", "content" => $aiModel->getDefaultPrompt()),
        array("role" => "user", "content" => "summarize the input transcript in same language as input, some text in the transcripts may interpreted wrongly and you should fix it to make sense based on the context back and forth, output with the following format:
        <<title>>
        brief introduction
        summarize in points with categories
        conclusion
        "),
        array("role" => "assistant", "content" => "Got it, I'm ready!"),
        array("role" => "user", "content" => $userInput)
    );

    $data = array(
        'messages' => $messages,
        'temperature' => 0.1,
        "top_p" => $aiModel->getTopP(),
        "frequency_penalty" => $aiModel->getFreqPenalty(),
        "presence_penalty" => $aiModel->getPresencePenalty(),
        "max_tokens" => $aiModel->getMaxCompletionTokens(),
        'stream' => true // Enable streaming
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "api-key: $apiKey"
    ));
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) {
        $lines = chunkMsgToArray($data);

        $line_c = count($lines);
        foreach($lines as $li=>$line){
            if(trim($line) == '[DONE]') {
                echo "event: close".PHP_EOL;
                echo "data: Connection closed".PHP_EOL.PHP_EOL;
                flush();
                break;
            }
            $line_data = json_decode($line, TRUE);
            if (isset($line_data['choices'][0]['delta']) && isset($line_data['choices'][0]['delta']['content']) && !empty($line_data['choices'][0]['delta']['content'])) {
                echo 'data: '.json_encode(['time'=>date('Y-m-d H:i:s'), 'content'=>$line_data['choices'][0]['delta']['content']]).PHP_EOL.PHP_EOL;
                flush();
            }
        }

        return strlen($data);
    });

    curl_exec($ch);
    curl_close($ch);
}

// Handle POST request to create a session
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['inputText'])) {
    // Generate a unique session ID
    $sessionId = uniqid('summary_', true);

    // Store the input text in the session
    $_SESSION[$sessionId] = $_POST['inputText'];

    // Return the session ID
    echo $sessionId;
    exit;
}

// Handle GET request with session parameter
if (isset($_GET['session'])) {
    $sessionId = $_GET['session'];

    // Check if the session exists
    if (isset($_SESSION[$sessionId])) {
        $inputText = $_SESSION[$sessionId];

        // Clear the session data after retrieving it
        unset($_SESSION[$sessionId]);

        // Generate the summary
        generateStreamedResponseFromOpenAI($inputText);
    } else {
        echo "event: error\ndata: Invalid or expired session\n\n";
        flush();
    }
} else {
    echo "event: error\ndata: No session ID provided\n\n";
    flush();
}
?>